const { PermissionFlagsBits, MessageFlags, EmbedBuilder } = require('discord.js');
const reactionRoleStorage = require('./reactionRoleStorage');

class ReactionRoleHandler {
    /**
     * Handle reaction role button interactions
     * @param {ButtonInteraction} interaction - The button interaction
     */
    async handleReactionRoleButton(interaction) {
        try {
            // Extract role ID from custom ID
            const roleId = interaction.customId.replace('reaction_role_', '');
            
            // Find the panel this button belongs to
            const panelData = await this.findPanelByMessage(interaction.guild.id, interaction.message.id);
            
            if (!panelData) {
                const embed = new EmbedBuilder()
                    .setDescription('❌ This reaction role panel is no longer configured. Please contact an administrator.')
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check if the role exists in the panel configuration
            const roleConfig = panelData.roles.find(r => r.role_id === roleId);
            if (!roleConfig) {
                const embed = new EmbedBuilder()
                    .setDescription('❌ This role is no longer configured for this panel.')
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            // Get the role object
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                const embed = new EmbedBuilder()
                    .setDescription('❌ This role no longer exists on the server.')
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check bot permissions
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            if (!botMember.permissions.has(PermissionFlagsBits.ManageRoles)) {
                const embed = new EmbedBuilder()
                    .setDescription('❌ I don\'t have permission to manage roles.')
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check role hierarchy
            if (role.position >= botMember.roles.highest.position) {
                const embed = new EmbedBuilder()
                    .setDescription(`❌ I cannot manage the role "${role.name}" because it's higher than or equal to my highest role.`)
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            if (role.managed) {
                const embed = new EmbedBuilder()
                    .setDescription(`❌ The role "${role.name}" is managed by an integration and cannot be assigned.`)
                    .setColor(0x000000); // Black color

                return interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

            // Get member
            const member = interaction.member;
            const hasRole = member.roles.cache.has(roleId);

            // Handle single role mode
            if (panelData.mode === 'single' && !hasRole) {
                await this.handleSingleRoleMode(interaction, member, role, panelData);
            } else {
                // Handle multi role mode or role removal
                await this.handleMultiRoleMode(interaction, member, role, hasRole);
            }

        } catch (error) {
            console.error('Error handling reaction role button:', error);
            
            if (!interaction.replied) {
                const embed = new EmbedBuilder()
                    .setDescription(`❌ An error occurred while processing your role request: ${error.message}`)
                    .setColor(0x000000); // Black color

                await interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    }

    /**
     * Handle single role mode (remove other roles from same panel)
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {GuildMember} member - The guild member
     * @param {Role} role - The role to assign
     * @param {Object} panelData - The panel configuration
     */
    async handleSingleRoleMode(interaction, member, role, panelData) {
        try {
            // Remove all other roles from this panel
            const rolesToRemove = [];
            for (const roleConfig of panelData.roles) {
                if (roleConfig.role_id !== role.id && member.roles.cache.has(roleConfig.role_id)) {
                    rolesToRemove.push(roleConfig.role_id);
                }
            }

            // Remove old roles and add new role
            if (rolesToRemove.length > 0) {
                await member.roles.remove(rolesToRemove);
            }
            await member.roles.add(role);

            const removedRoleNames = rolesToRemove.map(id => {
                const r = interaction.guild.roles.cache.get(id);
                return r ? r.name : 'Unknown Role';
            });

            let description = `✅ You now have the '${role.name}' role!`;
            if (removedRoleNames.length > 0) {
                description += `\n🔄 Removed: '${removedRoleNames.join("', '")}'`;
            }

            const embed = new EmbedBuilder()
                .setDescription(description)
                .setColor(0x000000); // Black color

            await interaction.reply({
                embeds: [embed],
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('Error in single role mode:', error);

            const embed = new EmbedBuilder()
                .setDescription(`❌ Failed to update your roles: ${error.message}`)
                .setColor(0x000000); // Black color

            await interaction.reply({
                embeds: [embed],
                flags: MessageFlags.Ephemeral
            });
        }
    }

    /**
     * Handle multi role mode (toggle role)
     * @param {ButtonInteraction} interaction - The button interaction
     * @param {GuildMember} member - The guild member
     * @param {Role} role - The role to toggle
     * @param {boolean} hasRole - Whether the member already has the role
     */
    async handleMultiRoleMode(interaction, member, role, hasRole) {
        try {
            if (hasRole) {
                // Remove role
                await member.roles.remove(role);

                const embed = new EmbedBuilder()
                    .setDescription(`🔄 Removed: '${role.name}'`)
                    .setColor(0x000000); // Black color

                await interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            } else {
                // Add role
                await member.roles.add(role);

                const embed = new EmbedBuilder()
                    .setDescription(`✅ You now have the '${role.name}' role!`)
                    .setColor(0x000000); // Black color

                await interaction.reply({
                    embeds: [embed],
                    flags: MessageFlags.Ephemeral
                });
            }

        } catch (error) {
            console.error('Error in multi role mode:', error);

            const embed = new EmbedBuilder()
                .setDescription(`❌ Failed to update your role: ${error.message}`)
                .setColor(0x000000); // Black color

            await interaction.reply({
                embeds: [embed],
                flags: MessageFlags.Ephemeral
            });
        }
    }

    /**
     * Find panel data by message ID
     * @param {string} guildId - Guild ID
     * @param {string} messageId - Message ID
     * @returns {Promise<Object|null>} Panel data or null if not found
     */
    async findPanelByMessage(guildId, messageId) {
        try {
            const guildPanels = await reactionRoleStorage.getPanelsByGuild(guildId);
            
            for (const [panelId, panelData] of Object.entries(guildPanels)) {
                if (panelData.message_id === messageId) {
                    return panelData;
                }
            }
            
            return null;
        } catch (error) {
            console.error('Error finding panel by message:', error);
            return null;
        }
    }

    /**
     * Check if a custom ID is a reaction role interaction
     * @param {string} customId - The custom ID to check
     * @returns {boolean} True if it's a reaction role interaction
     */
    isReactionRoleInteraction(customId) {
        return customId.startsWith('reaction_role_');
    }

    /**
     * Handle reaction role interactions (button clicks)
     * @param {ButtonInteraction} interaction - The button interaction
     */
    async handleReactionRoleInteraction(interaction) {
        if (this.isReactionRoleInteraction(interaction.customId)) {
            await this.handleReactionRoleButton(interaction);
        }
    }

    /**
     * Create a clean reaction role panel with custom button colors
     * @param {TextChannel} channel - The channel to send the panel to
     * @param {Object} panelData - Panel configuration data
     * @param {Guild} guild - The Discord guild
     * @returns {Promise<Object>} Result object with success status
     */
    async createReactionRolePanel(channel, panelData, guild) {
        try {
            const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

            // Create the main embed - clean and simple
            const embed = new EmbedBuilder()
                .setTitle(panelData.title)
                .setDescription(panelData.description)
                .setColor(panelData.color ? parseInt(panelData.color.replace('#', ''), 16) : 0x5865F2)
                .setTimestamp();

            if (panelData.imageUrl) {
                embed.setImage(panelData.imageUrl);
            }

            // Set footer - use custom footer if provided, otherwise use default mode info
            if (panelData.footer) {
                embed.setFooter({
                    text: panelData.footer,
                    iconURL: guild.iconURL()
                });
            } else {
                embed.setFooter({
                    text: `Mode: ${panelData.mode === 'single' ? 'Single Role (selecting one removes others)' : 'Multiple Roles (toggle on/off)'} • ${panelData.roles.length} role${panelData.roles.length !== 1 ? 's' : ''} available`,
                    iconURL: guild.iconURL()
                });
            }

            // Create buttons (up to 25 roles, 5 per row)
            const components = [];
            const roles = panelData.roles;

            for (let i = 0; i < roles.length; i += 5) {
                const row = new ActionRowBuilder();
                const rowRoles = roles.slice(i, i + 5);

                for (const role of rowRoles) {
                    const button = new ButtonBuilder()
                        .setCustomId(`reaction_role_${role.role_id}`)
                        .setLabel(role.label)
                        .setStyle(role.buttonStyle || ButtonStyle.Secondary);

                    // Only set emoji if it exists, is not null/empty, and is not the default theater mask
                    if (role.emoji && role.emoji.trim() !== '' && role.emoji !== '🎭') {
                        button.setEmoji(role.emoji);
                    }

                    row.addComponents(button);
                }

                components.push(row);
            }

            // Send only the main panel message - no extra embeds
            const message = await channel.send({
                embeds: [embed],
                components: components
            });

            // Store the panel data
            const panelId = `panel_${guild.id}_${Date.now()}`;
            const fullPanelData = {
                ...panelData,
                panel_id: panelId,
                guild_id: guild.id,
                channel_id: channel.id,
                message_id: message.id,
                created_at: new Date().toISOString()
            };

            console.log('[DEBUG] Saving panel to storage:', panelId, fullPanelData);
            await reactionRoleStorage.addPanel(panelId, fullPanelData);
            console.log('[DEBUG] Panel saved successfully to JSON file');

            return {
                success: true,
                panelId: panelId,
                messageId: message.id
            };

        } catch (error) {
            console.error('Error creating reaction role panel:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = new ReactionRoleHandler();
