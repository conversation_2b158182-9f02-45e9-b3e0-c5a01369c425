const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    MessageFlags,
    EmbedBuilder
} = require('discord.js');
const reactionRoleStorage = require('../utils/reactionRoleStorage');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('remove-reaction-panel')
        .setDescription('Remove a reaction role panel')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addStringOption(option =>
            option.setName('panel_id')
                .setDescription('The panel ID to remove (optional - will show list if not provided)')
                .setRequired(false)),

    async execute(interaction) {
        try {
            const panelId = interaction.options.getString('panel_id');

            if (!panelId) {
                // Show list of panels for this guild
                await this.showPanelList(interaction);
                return;
            }

            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            // Get panel data
            const panelData = await reactionRoleStorage.getPanel(panelId);
            
            if (!panelData) {
                return interaction.editReply({
                    content: `❌ Panel with ID \`${panelId}\` not found.`
                });
            }

            // Check if panel belongs to this guild
            if (panelData.guild_id !== interaction.guild.id) {
                return interaction.editReply({
                    content: `❌ Panel with ID \`${panelId}\` does not belong to this server.`
                });
            }

            // Try to delete the Discord message
            let messageDeleted = false;
            try {
                const channel = interaction.guild.channels.cache.get(panelData.channel_id);
                if (channel) {
                    const message = await channel.messages.fetch(panelData.message_id);
                    if (message) {
                        await message.delete();
                        messageDeleted = true;
                    }
                }
            } catch (error) {
                console.log(`Could not delete message for panel ${panelId}:`, error.message);
                // Continue with panel removal even if message deletion fails
            }

            // Remove panel from storage
            const removed = await reactionRoleStorage.removePanel(panelId);
            
            if (removed) {
                const statusMessage = messageDeleted 
                    ? `✅ Panel \`${panelId}\` and its message have been successfully removed.`
                    : `✅ Panel \`${panelId}\` has been removed from storage. (Message could not be deleted - it may have been manually deleted already)`;
                
                await interaction.editReply({
                    content: statusMessage
                });
            } else {
                await interaction.editReply({
                    content: `❌ Failed to remove panel \`${panelId}\` from storage.`
                });
            }

        } catch (error) {
            console.error('Error in remove-reaction-panel command:', error);
            
            const errorMessage = `❌ An error occurred while removing the panel: ${error.message}`;
            
            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    /**
     * Show list of panels for the current guild
     * @param {CommandInteraction} interaction - The Discord interaction
     */
    async showPanelList(interaction) {
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            const guildPanels = await reactionRoleStorage.getPanelsByGuild(interaction.guild.id);
            const panelEntries = Object.entries(guildPanels);

            if (panelEntries.length === 0) {
                return interaction.editReply({
                    content: '📋 No reaction role panels found in this server.\n\nUse `/setup-reaction-roles` to create one!'
                });
            }

            const embed = new EmbedBuilder()
                .setTitle('📋 Reaction Role Panels')
                .setDescription('Here are all the reaction role panels in this server:')
                .setColor(0x5865F2)
                .setFooter({ text: `Total panels: ${panelEntries.length}` });

            let description = '';
            for (const [panelId, panelData] of panelEntries) {
                const channel = interaction.guild.channels.cache.get(panelData.channel_id);
                const channelName = channel ? `#${channel.name}` : 'Unknown Channel';
                const mode = panelData.mode === 'multi' ? 'Multiple' : 'Single';
                const roleCount = panelData.roles ? panelData.roles.length : 0;

                description += `**Panel ID:** \`${panelId}\`\n`;
                description += `**Title:** ${panelData.title}\n`;
                description += `**Channel:** ${channelName}\n`;
                description += `**Mode:** ${mode} role\n`;
                description += `**Roles:** ${roleCount}\n`;
                description += `**Remove:** \`/remove-reaction-panel panel_id:${panelId}\`\n\n`;

                // Discord embed description limit is 4096 characters
                if (description.length > 3500) {
                    description += `... and ${panelEntries.length - panelEntries.indexOf([panelId, panelData]) - 1} more panels.\n`;
                    description += `Use \`/remove-reaction-panel panel_id:<panel_id>\` to remove a specific panel.`;
                    break;
                }
            }

            embed.setDescription(description);

            await interaction.editReply({
                embeds: [embed]
            });

        } catch (error) {
            console.error('Error showing panel list:', error);
            
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ An error occurred while fetching the panel list: ${error.message}`
                });
            } else {
                await interaction.reply({
                    content: `❌ An error occurred while fetching the panel list: ${error.message}`,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    }
};
